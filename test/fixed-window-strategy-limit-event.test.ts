import type { StrategyEvents } from '../app/utils/limiter/strategies/strategy'
import { Emitter } from '@kdt310722/utils/event'
import { FixedWindowStrategy, type FixedWindowStrategyConfig } from '../app/utils/limiter/strategies/fixed-window'

interface TestResult {
    name: string
    passed: boolean
    error?: string
    details?: any
}

class TestRunner {
    private readonly results: TestResult[] = []

    async run(name: string, testFn: () => Promise<void> | void): Promise<void> {
        try {
            await testFn()
            this.results.push({ name, passed: true })
            console.log(`✅ ${name}`)
        } catch (error) {
            this.results.push({
                name,
                passed: false,
                error: error instanceof Error ? error.message : String(error),
            })

            console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`)
        }
    }

    printSummary(): void {
        const passed = this.results.filter((r) => r.passed).length
        const total = this.results.length
        console.log(`\n📊 Test Summary: ${passed}/${total} passed`)

        if (passed < total) {
            console.log('\n❌ Failed tests:')

            this.results.filter((r) => !r.passed).forEach((r) => {
                console.log(`  - ${r.name}: ${r.error}`)
            })
        }
    }
}

function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
}

async function testFixedWindowStrategy() {
    const runner = new TestRunner()

    // Test 1: Verify 'limit' event is emitted when capacity is exceeded
    await runner.run('Should emit limit event when capacity exceeded', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 2,
            window: 1000, // 1 second window
        }

        let limitEventCount = 0
        let lastUntilValue: Date | null = null
        let lastMetadata: any = null

        emitter.on('limit', (until, metadata) => {
            limitEventCount++
            lastUntilValue = until
            lastMetadata = metadata
            console.log(`🔔 Limit event #${limitEventCount} - Until: ${until.toISOString()}, Metadata:`, metadata)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity (2 requests)
        const promises = [
            strategy.schedule(() => sleep(100)),
            strategy.schedule(() => sleep(100)),
        ]

        // This should trigger the limit event
        const thirdPromise = strategy.schedule(() => sleep(100))

        await Promise.all([...promises, thirdPromise])

        if (limitEventCount === 0) {
            throw new Error('No limit event was emitted')
        }

        console.log(`📈 Total limit events emitted: ${limitEventCount}`)
    })

    // Test 2: Verify 'until' value accuracy by testing actual behavior
    await runner.run('Should provide accurate until timestamp', async () => {
        const emitter = new Emitter<StrategyEvents>()
        const windowSize = 500 // 500ms window

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: windowSize,
        }

        let capturedUntil: Date | null = null
        let limitEventTime: number | null = null

        emitter.on('limit', (until: Date, _metadata: any) => {
            capturedUntil = until
            limitEventTime = Date.now()
            console.log(`🔔 Limit event at: ${new Date(limitEventTime).toISOString()}`)
            console.log(`📅 Until time: ${until.toISOString()}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity
        await strategy.schedule(() => sleep(50))

        // This should trigger limit event
        await strategy.schedule(() => sleep(50))

        if (!capturedUntil || !limitEventTime) {
            throw new Error('No until value or limit event time captured')
        }

        // Wait until the 'until' time has passed
        const waitTime = capturedUntil.getTime() - Date.now()
        console.log(`⏳ Waiting ${waitTime}ms until reservoir should reset`)

        if (waitTime > 0) {
            await sleep(waitTime + 100) // Add 100ms buffer
        }

        // Test if we can execute a new request after the 'until' time
        const testAfterReset = Date.now()
        console.log(`🧪 Testing request at: ${new Date(testAfterReset).toISOString()}`)

        try {
            const result = await strategy.schedule(() => 'test-after-reset')
            console.log(`✅ Request executed successfully after reset: ${result}`)

            // Verify the timing was accurate (should be able to execute immediately)
            const executionTime = Date.now() - testAfterReset
            console.log(`⚡ Execution took: ${executionTime}ms`)

            if (executionTime > 200) { // Should execute quickly if reservoir was reset
                throw new Error(`Request took too long to execute (${executionTime}ms), reservoir may not have reset properly`)
            }
        } catch (error) {
            throw new Error(`Failed to execute request after 'until' time: ${error}`)
        }
    })

    // Test 3: Verify metadata is provided correctly
    await runner.run('Should provide correct metadata with limit event', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: 1000,
        }

        let capturedMetadata: any = null

        emitter.on('limit', (_until: Date, metadata: any) => {
            capturedMetadata = metadata
            console.log(`📋 Captured metadata:`, metadata)
            console.log(`📋 Metadata type: ${typeof metadata}`)
            console.log(`📋 Is object: ${typeof metadata === 'object' && metadata !== null}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity and trigger limit
        await strategy.schedule(() => sleep(50))
        await strategy.schedule(() => sleep(50))

        if (capturedMetadata === null) {
            throw new Error('No metadata captured')
        }

        if (typeof capturedMetadata !== 'object') {
            throw new TypeError(`Expected metadata to be object, got ${typeof capturedMetadata}`)
        }
    })

    // Test 4: Verify limit event is not duplicated within same window
    await runner.run('Should not emit duplicate limit events in same window', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: 2000, // 2 second window
        }

        let limitEventCount = 0

        emitter.on('limit', (_until: Date, _metadata: any) => {
            limitEventCount++
            console.log(`🔔 Limit event #${limitEventCount} at ${new Date().toISOString()}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity
        await strategy.schedule(() => sleep(50))

        // Multiple requests that should all be queued but only trigger one limit event
        const promises = [
            strategy.schedule(() => sleep(50)),
            strategy.schedule(() => sleep(50)),
            strategy.schedule(() => sleep(50)),
        ]

        await Promise.all(promises)

        console.log(`📈 Total limit events for multiple queued requests: ${limitEventCount}`)

        // Should only have 1 limit event despite multiple queued requests
        if (limitEventCount > 1) {
            throw new Error(`Expected 1 limit event, got ${limitEventCount}`)
        }
    })

    // Test 5: Test window reset behavior
    await runner.run('Should reset capacity after window expires', async () => {
        const emitter = new Emitter<StrategyEvents>()
        const windowSize = 300 // 300ms window

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: windowSize,
        }

        let limitEventCount = 0

        emitter.on('limit', (until: Date, _metadata: any) => {
            limitEventCount++
            console.log(`🔔 Limit event #${limitEventCount} - Until: ${until.toISOString()}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // First window - fill capacity
        await strategy.schedule(() => sleep(50))

        // This should trigger limit event
        const limitPromise = strategy.schedule(() => sleep(50))

        // Wait for window to reset
        await sleep(windowSize + 100)

        // Should be able to make request without limit event
        await strategy.schedule(() => sleep(50))

        await limitPromise

        console.log(`📈 Total limit events across windows: ${limitEventCount}`)

        // Should have exactly 1 limit event from the first window
        if (limitEventCount !== 1) {
            throw new Error(`Expected 1 limit event, got ${limitEventCount}`)
        }
    })

    runner.printSummary()
}

// Run tests
console.log('🧪 Starting FixedWindowStrategy Limit Event Tests\n')
testFixedWindowStrategy().catch(console.error)
