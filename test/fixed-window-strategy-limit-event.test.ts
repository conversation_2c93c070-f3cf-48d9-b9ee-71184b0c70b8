import type { StrategyEvents } from '../app/utils/limiter/strategies/strategy'
import { Emitter } from '@kdt310722/utils/event'
import { FixedWindowStrategy, type FixedWindowStrategyConfig } from '../app/utils/limiter/strategies/fixed-window'

interface TestResult {
    name: string
    passed: boolean
    error?: string
    details?: any
}

class TestRunner {
    private readonly results: TestResult[] = []

    async run(name: string, testFn: () => Promise<void> | void): Promise<void> {
        try {
            await testFn()
            this.results.push({ name, passed: true })
            console.log(`✅ ${name}`)
        } catch (error) {
            this.results.push({
                name,
                passed: false,
                error: error instanceof Error ? error.message : String(error),
            })

            console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`)
        }
    }

    printSummary(): void {
        const passed = this.results.filter((r) => r.passed).length
        const total = this.results.length
        console.log(`\n📊 Test Summary: ${passed}/${total} passed`)

        if (passed < total) {
            console.log('\n❌ Failed tests:')

            this.results.filter((r) => !r.passed).forEach((r) => {
                console.log(`  - ${r.name}: ${r.error}`)
            })
        }
    }
}

function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
}

async function testFixedWindowStrategy() {
    const runner = new TestRunner()

    // Test 1: Verify 'limit' event is emitted when capacity is exceeded
    await runner.run('Should emit limit event when capacity exceeded', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 2,
            window: 1000, // 1 second window
        }

        let limitEventCount = 0
        let lastUntilValue: Date | null = null
        let lastMetadata: any = null

        emitter.on('limit', (until, metadata) => {
            limitEventCount++
            lastUntilValue = until
            lastMetadata = metadata
            console.log(`🔔 Limit event #${limitEventCount} - Until: ${until.toISOString()}, Metadata:`, metadata)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity (2 requests)
        const promises = [
            strategy.schedule(() => sleep(100)),
            strategy.schedule(() => sleep(100)),
        ]

        // This should trigger the limit event
        const thirdPromise = strategy.schedule(() => sleep(100))

        await Promise.all([...promises, thirdPromise])

        if (limitEventCount === 0) {
            throw new Error('No limit event was emitted')
        }

        console.log(`📈 Total limit events emitted: ${limitEventCount}`)
    })

    // Test 2: Verify 'until' value accuracy
    await runner.run('Should provide accurate until timestamp', async () => {
        const emitter = new Emitter<StrategyEvents>()
        const windowSize = 500 // 500ms window

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: windowSize,
        }

        let capturedUntil: Date | null = null
        const testStartTime = Date.now()

        emitter.on('limit', (until: Date, _metadata: any) => {
            capturedUntil = until
            console.log(`🕐 Test start: ${new Date(testStartTime).toISOString()}`)
            console.log(`🕐 Until time: ${until.toISOString()}`)
            console.log(`⏱️  Time difference: ${until.getTime() - testStartTime}ms`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity
        await strategy.schedule(() => sleep(50))

        // This should trigger limit event
        await strategy.schedule(() => sleep(50))

        if (!capturedUntil) {
            throw new Error('No until value captured')
        }

        // Calculate expected next window
        const currentWindow = Math.floor(testStartTime / windowSize)
        const expectedNextWindow = (currentWindow + 1) * windowSize
        const actualUntil = capturedUntil.getTime()

        console.log(`📊 Current window: ${currentWindow}`)
        console.log(`📊 Expected next window: ${expectedNextWindow}`)
        console.log(`📊 Actual until: ${actualUntil}`)
        console.log(`📊 Difference: ${Math.abs(actualUntil - expectedNextWindow)}ms`)

        // Allow some tolerance for timing differences
        const tolerance = 50 // 50ms tolerance

        if (Math.abs(actualUntil - expectedNextWindow) > tolerance) {
            throw new Error(`Until timestamp inaccurate. Expected: ${expectedNextWindow}, Got: ${actualUntil}, Difference: ${Math.abs(actualUntil - expectedNextWindow)}ms`)
        }
    })

    // Test 3: Verify metadata is provided correctly
    await runner.run('Should provide correct metadata with limit event', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: 1000,
        }

        let capturedMetadata: any = null

        emitter.on('limit', (_until: Date, metadata: any) => {
            capturedMetadata = metadata
            console.log(`📋 Captured metadata:`, metadata)
            console.log(`📋 Metadata type: ${typeof metadata}`)
            console.log(`📋 Is object: ${typeof metadata === 'object' && metadata !== null}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity and trigger limit
        await strategy.schedule(() => sleep(50))
        await strategy.schedule(() => sleep(50))

        if (capturedMetadata === null) {
            throw new Error('No metadata captured')
        }

        if (typeof capturedMetadata !== 'object') {
            throw new TypeError(`Expected metadata to be object, got ${typeof capturedMetadata}`)
        }
    })

    // Test 4: Verify limit event is not duplicated within same window
    await runner.run('Should not emit duplicate limit events in same window', async () => {
        const emitter = new Emitter<StrategyEvents>()

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: 2000, // 2 second window
        }

        let limitEventCount = 0

        emitter.on('limit', (_until: Date, _metadata: any) => {
            limitEventCount++
            console.log(`🔔 Limit event #${limitEventCount} at ${new Date().toISOString()}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // Fill capacity
        await strategy.schedule(() => sleep(50))

        // Multiple requests that should all be queued but only trigger one limit event
        const promises = [
            strategy.schedule(() => sleep(50)),
            strategy.schedule(() => sleep(50)),
            strategy.schedule(() => sleep(50)),
        ]

        await Promise.all(promises)

        console.log(`📈 Total limit events for multiple queued requests: ${limitEventCount}`)

        // Should only have 1 limit event despite multiple queued requests
        if (limitEventCount > 1) {
            throw new Error(`Expected 1 limit event, got ${limitEventCount}`)
        }
    })

    // Test 5: Test window reset behavior
    await runner.run('Should reset capacity after window expires', async () => {
        const emitter = new Emitter<StrategyEvents>()
        const windowSize = 300 // 300ms window

        const config: FixedWindowStrategyConfig = {
            capacity: 1,
            window: windowSize,
        }

        let limitEventCount = 0

        emitter.on('limit', (until: Date, _metadata: any) => {
            limitEventCount++
            console.log(`🔔 Limit event #${limitEventCount} - Until: ${until.toISOString()}`)
        })

        const strategy = new FixedWindowStrategy(config, emitter)

        // First window - fill capacity
        await strategy.schedule(() => sleep(50))

        // This should trigger limit event
        const limitPromise = strategy.schedule(() => sleep(50))

        // Wait for window to reset
        await sleep(windowSize + 100)

        // Should be able to make request without limit event
        await strategy.schedule(() => sleep(50))

        await limitPromise

        console.log(`📈 Total limit events across windows: ${limitEventCount}`)

        // Should have exactly 1 limit event from the first window
        if (limitEventCount !== 1) {
            throw new Error(`Expected 1 limit event, got ${limitEventCount}`)
        }
    })

    runner.printSummary()
}

// Run tests
console.log('🧪 Starting FixedWindowStrategy Limit Event Tests\n')
testFixedWindowStrategy().catch(console.error)
