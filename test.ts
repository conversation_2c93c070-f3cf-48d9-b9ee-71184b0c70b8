import { sleep } from '@kdt310722/utils/promise'
import { Limiter } from './app/utils/limiter/limiter'

let count = 0

const limiter = new Limiter({
    maxRequestsPerSecond: 1,
})

limiter.on('limit', (until) => {
    console.log('Limit until:', until.toISOString())
})

const fake = async () => limiter.schedule(async () => {
    console.log('Request start:', ++count, new Date().toISOString())

    await sleep(1000)

    console.log('Request end:', count, new Date().toISOString())
})

for (let i = 0; i < 3; i++) {
    fake()
}
