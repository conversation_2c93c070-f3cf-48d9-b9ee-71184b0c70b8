import { Limiter } from './app/utils/limiter/limiter'

let count = 1

const limiter = new Limiter({
    maxRequestsPerSecond: 1,
})

const fake = () => limiter.schedule(() => console.log('Request', ++count, new Date()))

async function main() {
    await limiter.schedule(() => console.log('Hello world!'))
    await limiter.schedule(() => console.log('Hello world!'))
    await limiter.schedule(() => console.log('Hello world!'))
}

main()
